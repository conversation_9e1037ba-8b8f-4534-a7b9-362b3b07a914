"use client";

import { useEffect, useState, useRef, useCallback } from "react";

interface UseScrollHideOptions {
  threshold?: number; // Minimum scroll distance to trigger hide/show
  debounceMs?: number; // Debounce delay for scroll events
  hideOnScrollDown?: boolean; // Whether to hide when scrolling down (default: true)
  initialVisible?: boolean; // Initial visibility state (default: true)
}

interface UseScrollHideReturn {
  isVisible: boolean;
  scrollDirection: 'up' | 'down' | null;
  scrollY: number;
}

export function useScrollHide(
  scrollContainer?: React.RefObject<HTMLElement | null>,
  options: UseScrollHideOptions = {}
): UseScrollHideReturn {
  const {
    threshold = 10,
    debounceMs = 16, // ~60fps
    hideOnScrollDown = true,
    initialVisible = true,
  } = options;

  const [isVisible, setIsVisible] = useState(initialVisible);
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const [scrollY, setScrollY] = useState(0);

  const lastScrollY = useRef(0);
  const ticking = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateScrollState = useCallback(() => {
    const container = scrollContainer?.current || window;
    const currentScrollY = scrollContainer?.current 
      ? scrollContainer.current.scrollTop 
      : window.scrollY;

    const scrollDiff = currentScrollY - lastScrollY.current;
    const absScrollDiff = Math.abs(scrollDiff);

    // Only update if scroll difference exceeds threshold
    if (absScrollDiff < threshold) {
      ticking.current = false;
      return;
    }

    const direction = scrollDiff > 0 ? 'down' : 'up';
    setScrollDirection(direction);
    setScrollY(currentScrollY);

    // Update visibility based on scroll direction
    if (hideOnScrollDown) {
      if (direction === 'down' && currentScrollY > threshold) {
        setIsVisible(false);
      } else if (direction === 'up') {
        setIsVisible(true);
      }
    } else {
      // Reverse behavior: hide on scroll up, show on scroll down
      if (direction === 'up' && currentScrollY > threshold) {
        setIsVisible(false);
      } else if (direction === 'down') {
        setIsVisible(true);
      }
    }

    lastScrollY.current = currentScrollY;
    ticking.current = false;
  }, [threshold, hideOnScrollDown, scrollContainer]);

  const requestTick = useCallback(() => {
    if (!ticking.current) {
      ticking.current = true;
      requestAnimationFrame(updateScrollState);
    }
  }, [updateScrollState]);

  const handleScroll = useCallback(() => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Debounce scroll events
    timeoutRef.current = setTimeout(() => {
      requestTick();
    }, debounceMs);
  }, [requestTick, debounceMs]);

  useEffect(() => {
    const container = scrollContainer?.current || window;
    
    // Set initial scroll position
    const initialScrollY = scrollContainer?.current 
      ? scrollContainer.current.scrollTop 
      : window.scrollY;
    
    lastScrollY.current = initialScrollY;
    setScrollY(initialScrollY);

    // Add scroll listener
    if (scrollContainer?.current) {
      scrollContainer.current.addEventListener('scroll', handleScroll, { passive: true });
    } else {
      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      // Cleanup
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      if (scrollContainer?.current) {
        scrollContainer.current.removeEventListener('scroll', handleScroll);
      } else {
        window.removeEventListener('scroll', handleScroll);
      }
    };
  }, [handleScroll, scrollContainer]);

  // Reset visibility when scroll container changes
  useEffect(() => {
    setIsVisible(initialVisible);
    setScrollDirection(null);
  }, [scrollContainer, initialVisible]);

  return {
    isVisible,
    scrollDirection,
    scrollY,
  };
}
